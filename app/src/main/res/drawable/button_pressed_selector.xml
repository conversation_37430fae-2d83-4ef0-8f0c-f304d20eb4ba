<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="oval">
            <solid android:color="#80FF9800" />
            <stroke android:width="2dp" android:color="@color/orangemuda" />
        </shape>
    </item>
    
    <!-- Focused state -->
    <item android:state_focused="true">
        <shape android:shape="oval">
            <solid android:color="#40FF9800" />
            <stroke android:width="1dp" android:color="@color/orangemuda" />
        </shape>
    </item>
    
    <!-- Normal state -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
    
</selector>
