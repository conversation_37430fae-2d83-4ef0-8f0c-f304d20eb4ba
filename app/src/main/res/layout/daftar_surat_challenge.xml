<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">
    <LinearLayout
        android:id="@+id/left_track"
        android:layout_marginTop="-10dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:orientation="horizontal">
                <ImageView
                    android:id="@+id/circle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/left_this_active"/>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/layout_left"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginLeft="20dp">
                <ImageView
                    android:id="@+id/btn_surat"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_gravity="center_vertical"
                    android:scaleType="centerCrop" />
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginLeft="16dp"
                    android:layout_gravity="center_vertical">
                    <TextView
                        android:id="@+id/urutan_audio"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="#777777"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/ayat"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text=""
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:id="@+id/tandai_hafal_left"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_pressed_menu"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="horizontal">
                        <ImageView
                            android:id="@+id/ic_tandai_hafal_left"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                        <TextView
                            android:id="@+id/text_hafal_left"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:textSize="12dp"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
            <!-- Floating "Mulai" Text -->
            <androidx.cardview.widget.CardView
                android:id="@+id/floating_mulai_card"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginTop="30dp"
                android:visibility="gone"
                app:cardBackgroundColor="@color/orangemuda"
                app:cardCornerRadius="20dp"
                app:cardElevation="8dp">

                <TextView
                    android:id="@+id/floating_mulai_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Mulai"
                    android:textColor="@android:color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:paddingLeft="16dp"
                    android:paddingRight="16dp"
                    android:paddingTop="8dp"
                    android:paddingBottom="8dp" />

                <!-- Triangle pointer -->
                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_gravity="bottom|center_horizontal"
                    android:layout_marginBottom="-8dp"
                    android:background="@drawable/triangle_pointer" />

            </androidx.cardview.widget.CardView>

            <LinearLayout
                android:id="@+id/layout_right"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="right"
                android:layout_marginTop="10dp"
                android:layout_marginRight="20dp">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="right"
                    android:layout_marginRight="16dp"
                    android:layout_gravity="center_vertical">
                    <TextView
                        android:id="@+id/urutan_audio2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="#777777"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/ayat2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text=""
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold" />
                    <LinearLayout
                        android:id="@+id/tandai_hafal_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_pressed_menu"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:orientation="horizontal">
                        <TextView
                            android:id="@+id/text_hafal_right"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:textSize="12dp"/>
                        <ImageView
                            android:id="@+id/ic_tandai_hafal_right"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"/>
                    </LinearLayout>
                </LinearLayout>
                <ImageView
                    android:id="@+id/btn_surat2"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_gravity="center_vertical"
                    android:scaleType="centerCrop" />
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>