<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F8F8F8"
    android:fitsSystemWindows="true">

    <!-- Header with back button and title -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        android:background="@android:color/white">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/back_button"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_back"
                android:tint="@color/orangemuda"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="16dp"
            android:text="QS. An-Naba 1-5"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/orangemuda" />

    </LinearLayout>

    <!-- Content area -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Progress bar -->
            <ProgressBar
                android:id="@+id/progress_bar"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:progress="0"
                android:max="100"
                android:progressDrawable="@drawable/progress_bar_drawable"
                android:layout_marginBottom="32dp" />

            <!-- Main instruction -->

            <TextView
                android:id="@+id/instruction_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="Tarik napas dalam-dalam."
                android:textColor="#333333"
                android:textSize="24sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/instruction_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Di halaman ini, kamu akan sambung ayat bersama Hamim. Hamim akan membaca satu ayat, lalu giliran kamu melanjutkan ayat berikutnya."
                android:textSize="16sp"
                android:textColor="#666666"
                android:lineSpacingMultiplier="1.2"
                android:layout_marginBottom="24dp" />

            <!-- Note section -->
            <TextView
                android:id="@+id/note_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Catatan:"
                android:textSize="14sp"
                android:textColor="#999999"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/note_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Pastikan suaramu jelas dan lingkungan sekitar tenang."
                android:textSize="14sp"
                android:textColor="#999999"
                android:layout_marginBottom="32dp" />

            <!-- Action text -->
            <TextView
                android:id="@+id/action_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Tekan &quot;Mulai&quot; saat kamu siap."
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="@color/orangemuda"
                android:layout_marginBottom="16dp" />

            <!-- Ayat display (hidden initially) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/ayat_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp"
                app:cardBackgroundColor="#FFF3E0">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="20dp">

                    <!-- Ayat number -->
                    <TextView
                        android:id="@+id/ayat_number"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1"
                        android:textSize="14sp"
                        android:textColor="@color/orangemuda"
                        android:background="@drawable/circle_background"
                        android:gravity="center"
                        android:layout_gravity="end"
                        android:layout_marginBottom="12dp"
                        android:minWidth="32dp"
                        android:minHeight="32dp" />

                    <!-- Ayat text (Arabic) -->
                    <TextView
                        android:id="@+id/ayat_text"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="بِسْمِ اللَّهِ الرَّحْمَٰنِ الرَّحِيمِ"
                        android:textSize="20sp"
                        android:textColor="#333333"
                        android:gravity="end"
                        android:layout_marginBottom="12dp"
                        android:lineSpacingExtra="8dp" />

                    <!-- Ayat translation -->
                    <TextView
                        android:id="@+id/ayat_translation"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Dengan nama Allah Yang Maha Pengasih, Maha Penyayang."
                        android:textSize="14sp"
                        android:textColor="#666666"
                        android:gravity="start"
                        android:lineSpacingExtra="4dp" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- Mulai button -->
            <Button
                android:id="@+id/mulai_button"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Mulai"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:background="@drawable/rounded_button_orange"
                android:layout_marginBottom="32dp" />

            <!-- Spacer to push content up -->
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

        </LinearLayout>

    </ScrollView>

    <!-- Status Text (for state 2 and 3) -->
    <TextView
        android:id="@+id/status_text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="DENGARKAN AYAT"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/orangemuda"
        android:gravity="center"
        android:padding="16dp"
        android:background="#FFF3E0"
        android:visibility="gone" />

    <!-- Bottom icons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        android:padding="24dp"
        android:background="@android:color/white">

        <androidx.cardview.widget.CardView
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_marginEnd="32dp"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center">

                <ImageView
                    android:id="@+id/hamim_icon"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:scaleType="centerCrop" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <androidx.cardview.widget.CardView
            android:layout_width="70dp"
            android:layout_height="70dp"
            app:cardBackgroundColor="@android:color/transparent"
            app:cardCornerRadius="16dp"
            app:cardElevation="0dp">

            <ImageView
                android:id="@+id/mic_icon"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_gravity="center"
                android:scaleType="centerCrop" />

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</LinearLayout>
