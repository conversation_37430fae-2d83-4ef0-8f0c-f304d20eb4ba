package co.metode.hamim.challenge;

import android.app.Activity;
import android.content.Context;
import android.net.ConnectivityManager;
import android.util.Log;
import android.widget.Toast;

import java.util.List;
import java.util.concurrent.ExecutorService;

import co.metode.hamim.AudioChallenge;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.cek_hafalan.ItemModel;
import co.metode.hamim.cek_hafalan.database.HafalanDatabase;
import co.metode.hamim.cek_hafalan.database.OfflineActionEntity;
import co.metode.hamim.complete_surat.CompleteSurat;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Manages hafal (memorization) status operations for challenge items
 * Handles both online and offline hafal marking with synchronization
 */
public class ChallengeHafalManager {
    
    private final Context context;
    private final String idUser;
    private final HafalanDatabase database;
    private final ExecutorService executorService;
    private final List<ItemModel> itemList;
    private ApiInterface apiInterface;

    public ChallengeHafalManager(Context context, String idUser, HafalanDatabase database,
                                ExecutorService executorService, List<ItemModel> itemList) {
        this.context = context;
        this.idUser = idUser;
        this.database = database;
        this.executorService = executorService;
        this.itemList = itemList;
    }

    /**
     * Handle hafal marking for a specific position
     */
    public void handleHafalClick(int position, String id_surat, String id_detail_surat, 
                                OnHafalStatusChangeListener listener) {
        
        if (position > 0 && !isPositionReady(position - 1)) {
            Toast.makeText(context, "Selesaikan dulu hafalan sebelumnya", Toast.LENGTH_SHORT).show();
            return;
        }

        ItemModel currentItem = itemList.get(position);
        
        if (currentItem.getIs_hafal() == 100) {
            int lastMarkedPosition = findLastMarkedPosition();
            if (position != lastMarkedPosition) {
                Toast.makeText(context, "Batalkan terlebih dahulu hafalan setelah ini", 
                             Toast.LENGTH_SHORT).show();
                return;
            }
        }

        if (isNetworkAvailable()) {
            handleOnlineHafalUpdate(position, id_surat, id_detail_surat, listener);
        } else {
            handleOfflineHafalUpdate(position, id_surat, id_detail_surat, listener);
        }
    }

    /**
     * Handle online hafal update via API
     */
    private void handleOnlineHafalUpdate(int position, String id_surat, String id_detail_surat,
                                        OnHafalStatusChangeListener listener) {
        apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<CompleteSurat> call = apiInterface.tandaiHafalRespons(idUser, id_surat, id_detail_surat);
        
        call.enqueue(new Callback<CompleteSurat>() {
            @Override
            public void onResponse(Call<CompleteSurat> call, Response<CompleteSurat> response) {
                handleHafalResponse(response, position, listener);
                refreshParentActivity();
            }

            @Override
            public void onFailure(Call<CompleteSurat> call, Throwable t) {
                Log.e("ChallengeHafalManager", "Error tandaiHafal", t);
                Toast.makeText(context, t.getLocalizedMessage(), Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * Handle offline hafal update with local database
     */
    private void handleOfflineHafalUpdate(int position, String id_surat, String id_detail_surat,
                                         OnHafalStatusChangeListener listener) {
        ItemModel currentItem = itemList.get(position);
        final int newStatus = currentItem.getIs_hafal() == 100 ? 0 : 100;

        executorService.execute(() -> {
            try {
                // Update the entity in the database
                database.hafalanDao().updateHafalStatus(id_surat, idUser, newStatus);

                // Store offline action to sync later when online
                database.offlineActionDao().insert(new OfflineActionEntity(
                        idUser,
                        id_surat,
                        id_detail_surat,
                        newStatus == 100 ? "mark" : "unmark",
                        System.currentTimeMillis()
                ));

                // Update UI on main thread
                ((Activity) context).runOnUiThread(() -> {
                    updateItemStatus(position, newStatus, listener);
                    refreshParentActivity();
                });
            } catch (Exception e) {
                Log.e("ChallengeHafalManager", "Error updating offline data", e);
                ((Activity) context).runOnUiThread(() -> {
                    Toast.makeText(context, "Gagal memperbarui data offline: " + e.getMessage(), 
                                 Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    /**
     * Handle API response for hafal status change
     */
    private void handleHafalResponse(Response<CompleteSurat> response, int position,
                                    OnHafalStatusChangeListener listener) {
        if (response.isSuccessful() && response.body() != null) {
            CompleteSurat completeSurat = response.body();
            
            if (position >= 0 && position < itemList.size()) {
                int newStatus = completeSurat.getData() == 1 ? 100 : 0;
                updateItemStatus(position, newStatus, listener);
            } else {
                Log.e("ChallengeHafalManager", "Invalid position: " + position);
            }
        } else {
            Toast.makeText(context, "Gagal memproses hafalan", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Update item status and notify listener
     */
    private void updateItemStatus(int position, int newStatus, OnHafalStatusChangeListener listener) {
        itemList.get(position).setIs_hafal(newStatus);
        
        if (newStatus == 100) {
            listener.onHafalStatusChanged(position, true);
            Toast.makeText(context, "Hafalan Ditambahkan", Toast.LENGTH_SHORT).show();
        } else {
            listener.onHafalStatusChanged(position, false);
            Toast.makeText(context, "Hafalan Dibatalkan", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * Sync offline actions when network becomes available
     */
    public void syncOfflineActions() {
        if (!isNetworkAvailable()) {
            return;
        }

        executorService.execute(() -> {
            List<OfflineActionEntity> offlineActions = database.offlineActionDao().getAllPendingActions();

            for (OfflineActionEntity action : offlineActions) {
                try {
                    apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
                    Call<CompleteSurat> call = apiInterface.tandaiHafalRespons(
                            action.getUserId(),
                            action.getSuratId(),
                            action.getDetailSuratId());

                    Response<CompleteSurat> response = call.execute();

                    if (response.isSuccessful() && response.body() != null) {
                        database.offlineActionDao().delete(action);
                    }
                } catch (Exception e) {
                    Log.e("ChallengeHafalManager", "Error syncing offline action", e);
                }
            }

            ((Activity) context).runOnUiThread(this::refreshParentActivity);
        });
    }

    /**
     * Check if network is available
     */
    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = 
            (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            android.net.NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    /**
     * Check if position is ready (all previous items are completed)
     */
    private boolean isPositionReady(int position) {
        if (position <= 0) return true;

        for (int i = 0; i < position; i++) {
            if (itemList.get(i).getIs_hafal() != 100) {
                return false;
            }
        }
        return true;
    }

    /**
     * Find the last marked position
     */
    private int findLastMarkedPosition() {
        for (int i = itemList.size() - 1; i >= 0; i--) {
            if (itemList.get(i).getIs_hafal() == 100) {
                return i;
            }
        }
        return -1;
    }

    /**
     * Refresh parent activity
     */
    private void refreshParentActivity() {
        if (context instanceof AudioChallenge) {
            ((AudioChallenge) context).refreshPage();
        }
    }

    /**
     * Interface for hafal status change callbacks
     */
    public interface OnHafalStatusChangeListener {
        void onHafalStatusChanged(int position, boolean isHafal);
    }
}
