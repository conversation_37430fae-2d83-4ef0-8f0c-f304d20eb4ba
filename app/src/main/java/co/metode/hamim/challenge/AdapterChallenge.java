package co.metode.hamim.challenge;

import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import co.metode.hamim.AudioChallenge;
import co.metode.hamim.Play;
import co.metode.hamim.R;
import co.metode.hamim.SambungAyatInstructionActivity;
import co.metode.hamim.cek_hafalan.ItemModel;
import co.metode.hamim.cek_hafalan.database.HafalanDatabase;
import co.metode.hamim.surat.detailSurat.database.SuratDetailDatabase;

/**
 * Main adapter for Challenge RecyclerView
 * Coordinates between different modular components to handle challenge list functionality
 */
public class AdapterChallenge extends RecyclerView.Adapter<ChallengeViewHolder>
        implements ChallengeItemBinder.OnItemClickListener,
                   ChallengeDownloadManager.OnDownloadCompleteListener,
                   ChallengeDownloadManager.OnAudioCheckListener,
                   ChallengeHafalManager.OnHafalStatusChangeListener {

    private final Context context;
    private final List<ItemModel> itemList;
    private final ArrayList<String> arrayListNamaSurat;
    private final ArrayList<String> arrayListUrl;
    private final String idUser;
    private final String namaJuz;

    // Modular components
    private ChallengeItemBinder itemBinder;
    private ChallengeDownloadManager downloadManager;
    private ChallengeHafalManager hafalManager;

    // Database and threading
    private final SuratDetailDatabase suratDetailDatabase;
    private final Executor executor;
    private final Handler mainHandler = new Handler(Looper.getMainLooper());
    private final HafalanDatabase database;
    private final ExecutorService executorService;

    // Asset bitmaps
    private Bitmap ayatButtonBitmap;
    private Bitmap sambungAyatButtonBitmap;

    // Listener
    private OnHafalStatusChangedListener listener;

    public AdapterChallenge(Context context, List<ItemModel> itemList,
                           ArrayList<String> arrayListNamaSurat, ArrayList<String> arrayListUrl,
                           String idUser, String namaJuz) {
        this.context = context;
        this.itemList = itemList;
        this.arrayListNamaSurat = arrayListNamaSurat;
        this.arrayListUrl = arrayListUrl;
        this.idUser = idUser;
        this.namaJuz = namaJuz;

        // Initialize databases and threading
        this.suratDetailDatabase = SuratDetailDatabase.getDatabase(context);
        this.executor = Executors.newSingleThreadExecutor();
        this.database = HafalanDatabase.getDatabase(context);
        this.executorService = Executors.newSingleThreadExecutor();

        initializeComponents();
        initializeLists();
        loadAssetImages();
    }

    /**
     * Initialize modular components
     */
    private void initializeComponents() {
        downloadManager = new ChallengeDownloadManager(context, suratDetailDatabase, executor, mainHandler);
        hafalManager = new ChallengeHafalManager(context, idUser, database, executorService, itemList);
    }

    /**
     * Initialize lists with item data
     */
    private void initializeLists() {
        for (ItemModel item : itemList) {
            arrayListNamaSurat.add(item.getJumlahAyat());
            arrayListUrl.add(item.getUrl_audio());
        }
    }

    /**
     * Load asset images for buttons
     */
    private void loadAssetImages() {
        try {
            AssetManager assetManager = context.getAssets();

            // Load button-ayat.png
            java.io.InputStream ayatInputStream = assetManager.open("button-ayat.png");
            ayatButtonBitmap = BitmapFactory.decodeStream(ayatInputStream);
            ayatInputStream.close();

            // Load button-sambung-ayat.png
            java.io.InputStream sambungInputStream = assetManager.open("button-sambung-ayat.png");
            sambungAyatButtonBitmap = BitmapFactory.decodeStream(sambungInputStream);
            sambungInputStream.close();

            // Initialize item binder after loading assets
            itemBinder = new ChallengeItemBinder(context, itemList, ayatButtonBitmap, sambungAyatButtonBitmap);

        } catch (java.io.IOException e) {
            Log.e("AdapterChallenge", "Failed to load asset images", e);
            // Initialize item binder with null bitmaps (will use fallback)
            itemBinder = new ChallengeItemBinder(context, itemList, null, null);
        }
    }

    @NonNull
    @Override
    public ChallengeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.daftar_surat_challenge, parent, false);
        return new ChallengeViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ChallengeViewHolder holder, int position) {
        final ItemModel item = itemList.get(position);
        String id_surat = item.getIdSurat();
        String id_detail_surat = item.getId_detail_surat();
        String title = item.getNamaSurat();
        String jumlah_ayat = item.getJumlah_ayat_potong();
        int isHafal = item.getIs_hafal();

        // Use item binder to bind data
        itemBinder.bindItem(holder, item, position, title, jumlah_ayat, isHafal,
                           id_surat, id_detail_surat, this);
    }

    @Override
    public int getItemCount() {
        return itemList.size();
    }

    // ChallengeItemBinder.OnItemClickListener implementation
    @Override
    public void onAudioClick(ItemModel item, int position) {
        downloadManager.checkAudioAvailability(item, position, this);
    }

    @Override
    public void onSambungAyatClick(String title, int position) {
        launchSambungAyatActivity(title, position);
    }

    @Override
    public void onHafalClick(int position) {
        ItemModel item = itemList.get(position);
        hafalManager.handleHafalClick(position, item.getIdSurat(),
                                     item.getId_detail_surat(), this);
    }

    // ChallengeDownloadManager.OnAudioCheckListener implementation
    @Override
    public void onAudioAvailable(ItemModel item, int position, String localPath) {
        launchPlayActivity(item, position, localPath);
    }

    @Override
    public void onAudioNotAvailable(ItemModel item, int position) {
        downloadManager.showDownloadDialog(item, position, this);
    }

    // ChallengeDownloadManager.OnDownloadCompleteListener implementation
    @Override
    public void onDownloadComplete(ItemModel item, int position, String localPath) {
        launchPlayActivity(item, position, localPath);
    }

    // ChallengeHafalManager.OnHafalStatusChangeListener implementation
    @Override
    public void onHafalStatusChanged(int position, boolean isHafal) {
        // Update the current item
        notifyItemChanged(position);

        // Update progress listener
        if (listener != null) {
            listener.onHafalStatusChanged(isHafal);
        }

        // Only update items that might be affected by the change
        updateAffectedItems(position, isHafal);
    }

    /**
     * Update only the items that are affected by the hafal status change
     * This is more efficient than refreshing the entire RecyclerView
     */
    private void updateAffectedItems(int changedPosition, boolean isHafal) {
        if (isHafal) {
            // If item was marked as hafal, enable the next item if it exists
            int nextPosition = changedPosition + 1;
            if (nextPosition < itemList.size()) {
                notifyItemChanged(nextPosition);
            }
        } else {
            // If item was unmarked, disable items after it that should be disabled
            // Only update a reasonable range to avoid too many updates
            int endPosition = Math.min(changedPosition + 5, itemList.size()); // Limit to next 5 items
            for (int i = changedPosition + 1; i < endPosition; i++) {
                notifyItemChanged(i);
            }
        }
    }

    /**
     * Check if an item should be disabled based on the changed position
     */
    private boolean shouldItemBeDisabled(int itemPosition, int changedPosition) {
        // If any item before this position (including the changed one) is not hafal,
        // then this item should be disabled
        for (int i = 0; i <= changedPosition; i++) {
            if (itemList.get(i).getIs_hafal() != 100) {
                return true;
            }
        }
        return false;
    }

    /**
     * Launch SambungAyat instruction activity
     */
    private void launchSambungAyatActivity(String title, int position) {
        Intent intent = new Intent(context, SambungAyatInstructionActivity.class);
        intent.putExtra("id_juz", ((AudioChallenge) context).getIntent().getStringExtra("id_juz"));

        String titleRange = title.replace("Hari ", "").replace(": ", " ");
        intent.putExtra("title_range", titleRange);

        context.startActivity(intent);
    }

    /**
     * Launch Play activity for audio playback
     */
    private void launchPlayActivity(ItemModel item, int position, String localPath) {
        File audioFile = new File(localPath);

        if (!audioFile.exists()) {
            localPath = item.getUrl_audio();
            Toast.makeText(context, "Local audio not found. Using original URL.", Toast.LENGTH_SHORT).show();
        }

        Intent intent = new Intent(context, Play.class);
        intent.putExtra("id_juz", ((AudioChallenge) context).getIntent().getStringExtra("id_juz"));
        intent.putExtra("id_surat", item.getIdSurat());
        intent.putExtra("halaman", item.getHalaman());
        intent.putExtra("id_detail_surat", item.getId_detail_surat());
        intent.putExtra("img", item.getGambar());
        intent.putExtra("nama_surat", item.getNamaSurat());
        intent.putExtra("nama_juz", namaJuz);
        intent.putExtra("jumlah_ayat", item.getJumlah_ayat_potong());
        intent.putExtra("arti_surat", item.getArtiSurat());
        intent.putExtra("url_audio", localPath);
        intent.putExtra("arrayListNamaSurat", arrayListNamaSurat);
        intent.putExtra("arrayListUrl", arrayListUrl);
        intent.putExtra("from", "audio_challenge");
        intent.putExtra("posisi", position);
        intent.putExtra("posisiMushaf", ((AudioChallenge) context).getIntent().getStringExtra("posisiMushaf"));
        intent.putExtra("id_user", idUser);
        intent.putExtra("from_detail_surat", false);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

        context.startActivity(intent);

        mainHandler.post(() ->
                Toast.makeText(context, "Playing surat " + item.getNamaSurat() + " " + item.getJumlah_ayat_potong(),
                        Toast.LENGTH_LONG).show()
        );
    }

    // Public interface and utility methods
    public interface OnHafalStatusChangedListener {
        void onHafalStatusChanged(boolean isHafal);
    }

    public void setOnHafalStatusChangedListener(OnHafalStatusChangedListener listener) {
        this.listener = listener;
    }

    public void syncOfflineActions() {
        hafalManager.syncOfflineActions();
    }

    /**
     * Update a specific item without triggering full RecyclerView refresh
     * This is much more efficient than notifyDataSetChanged()
     */
    public void updateItemHafalStatus(int position, int hafalStatus) {
        if (position >= 0 && position < itemList.size()) {
            itemList.get(position).setIs_hafal(hafalStatus);
            notifyItemChanged(position);
        }
    }

    /**
     * Update multiple items efficiently
     */
    public void updateItemsRange(int startPosition, int endPosition) {
        if (startPosition >= 0 && endPosition < itemList.size() && startPosition <= endPosition) {
            notifyItemRangeChanged(startPosition, endPosition - startPosition + 1);
        }
    }

    public void cleanup() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
