package co.metode.hamim.challenge;

import android.app.ProgressDialog;
import android.content.Context;
import android.graphics.drawable.ColorDrawable;
import android.os.Handler;
import android.widget.Button;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.core.content.ContextCompat;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.concurrent.Executor;

import co.metode.hamim.R;
import co.metode.hamim.cek_hafalan.ItemModel;
import co.metode.hamim.surat.audioFiles.database.AudioFilesEntity;
import co.metode.hamim.surat.detailSurat.database.SuratDetailDatabase;
import okhttp3.OkHttpClient;
import okhttp3.Request;

/**
 * Manages audio download functionality for challenge items
 * Handles download dialogs, progress tracking, and file management
 */
public class ChallengeDownloadManager {
    
    private final Context context;
    private final SuratDetailDatabase suratDetailDatabase;
    private final Executor executor;
    private final Handler mainHandler;

    public ChallengeDownloadManager(Context context, SuratDetailDatabase suratDetailDatabase, 
                                  Executor executor, Handler mainHandler) {
        this.context = context;
        this.suratDetailDatabase = suratDetailDatabase;
        this.executor = executor;
        this.mainHandler = mainHandler;
    }

    /**
     * Show download dialog for audio file
     */
    public void showDownloadDialog(ItemModel item, int position, OnDownloadCompleteListener listener) {
        AlertDialog dialog = new AlertDialog.Builder(context)
                .setTitle("Unduh Audio")
                .setMessage("Apakah Anda ingin mengunduh audio untuk " + item.getNamaSurat() + "?")
                .setPositiveButton("Ya", null)
                .setNegativeButton("Tidak", null)
                .setCancelable(true)
                .create();

        dialog.show();

        Button positiveButton = dialog.getButton(AlertDialog.BUTTON_POSITIVE);
        Button negativeButton = dialog.getButton(AlertDialog.BUTTON_NEGATIVE);

        if (positiveButton != null) {
            positiveButton.setTextColor(ContextCompat.getColor(context, R.color.orangemuda));
        }
        if (negativeButton != null) {
            negativeButton.setTextColor(ContextCompat.getColor(context, R.color.orangemuda));
        }

        positiveButton.setOnClickListener(v1 -> {
            startDownload(item, position, listener);
            dialog.dismiss();
        });

        negativeButton.setOnClickListener(v2 -> {
            // Use original URL for streaming
            listener.onDownloadComplete(item, position, item.getUrl_audio());
            dialog.dismiss();
        });
    }

    /**
     * Start downloading audio file
     */
    private void startDownload(ItemModel item, int position, OnDownloadCompleteListener listener) {
        ProgressDialog progressDialog = createProgressDialog();
        progressDialog.show();

        executor.execute(() -> {
            try {
                String fileName = "audio_" + item.getId_detail_surat() + ".mp3";
                downloadAudio(item.getUrl_audio(), fileName, item, position, progressDialog, listener);
            } catch (Exception e) {
                mainHandler.post(() -> {
                    progressDialog.dismiss();
                    Toast.makeText(context, "Error downloading audio: " + e.getMessage(), 
                                 Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    /**
     * Create progress dialog for download
     */
    private ProgressDialog createProgressDialog() {
        ProgressDialog progressDialog = new ProgressDialog(context);
        progressDialog.setMessage("Mengunduh audio...");
        progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        progressDialog.setProgress(0);
        progressDialog.setIndeterminateDrawable(
            new ColorDrawable(ContextCompat.getColor(context, R.color.orangemuda)));
        progressDialog.setCancelable(false);
        return progressDialog;
    }

    /**
     * Download audio file from URL
     */
    private void downloadAudio(String url, String fileName, ItemModel item, int position,
                              ProgressDialog progressDialog, OnDownloadCompleteListener listener) {
        File directory = new File(context.getFilesDir(), "audio");
        if (!directory.exists()) {
            directory.mkdirs();
        }

        File outputFile = new File(directory, fileName);

        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(url).build();

        client.newCall(request).enqueue(new okhttp3.Callback() {
            @Override
            public void onFailure(@NonNull okhttp3.Call call, @NonNull IOException e) {
                mainHandler.post(() -> {
                    progressDialog.dismiss();
                    Toast.makeText(context, "Download Gagal: Periksa koneksi internet anda", 
                                 Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onResponse(@NonNull okhttp3.Call call, @NonNull okhttp3.Response response) {
                if (!response.isSuccessful()) {
                    mainHandler.post(() -> {
                        progressDialog.dismiss();
                        android.util.Log.e("DownloadError", "Download failed: " + response.code());
                    });
                    return;
                }

                try (InputStream inputStream = response.body().byteStream();
                     OutputStream outputStream = new FileOutputStream(outputFile)) {
                    
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    outputStream.flush();

                    saveToDatabase(item, url, outputFile, position, progressDialog, listener);

                } catch (IOException e) {
                    mainHandler.post(() -> {
                        progressDialog.dismiss();
                        Toast.makeText(context, "Error downloading audio: " + e.getMessage(),
                                     Toast.LENGTH_SHORT).show();
                    });
                }
            }
        });
    }

    /**
     * Save downloaded file info to database
     */
    private void saveToDatabase(ItemModel item, String url, File outputFile, int position,
                               ProgressDialog progressDialog, OnDownloadCompleteListener listener) {
        executor.execute(() -> {
            try {
                AudioFilesEntity newAudioFile = new AudioFilesEntity(item.getId_detail_surat(), url);
                newAudioFile.setLocalPath(outputFile.getAbsolutePath());
                newAudioFile.setDownloaded(true);
                suratDetailDatabase.audioFileDao().insert(newAudioFile);

                mainHandler.post(() -> {
                    progressDialog.dismiss();
                    listener.onDownloadComplete(item, position, outputFile.getAbsolutePath());
                });
            } catch (Exception e) {
                mainHandler.post(() -> {
                    progressDialog.dismiss();
                    Toast.makeText(context, "Error saving audio: " + e.getMessage(),
                                 Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    /**
     * Check if audio file is already downloaded
     */
    public void checkAudioAvailability(ItemModel item, int position, OnAudioCheckListener listener) {
        executor.execute(() -> {
            try {
                AudioFilesEntity audioFile = suratDetailDatabase.audioFileDao()
                    .getAudioFile(item.getId_detail_surat());
                
                mainHandler.post(() -> {
                    if (audioFile != null && audioFile.isDownloaded()) {
                        listener.onAudioAvailable(item, position, audioFile.getLocalPath());
                    } else {
                        listener.onAudioNotAvailable(item, position);
                    }
                });
            } catch (Exception e) {
                mainHandler.post(() -> {
                    Toast.makeText(context, "Error checking audio: " + e.getMessage(), 
                                 Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    /**
     * Interface for download completion callback
     */
    public interface OnDownloadCompleteListener {
        void onDownloadComplete(ItemModel item, int position, String localPath);
    }

    /**
     * Interface for audio availability check callback
     */
    public interface OnAudioCheckListener {
        void onAudioAvailable(ItemModel item, int position, String localPath);
        void onAudioNotAvailable(ItemModel item, int position);
    }
}
