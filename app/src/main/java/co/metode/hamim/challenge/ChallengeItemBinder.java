package co.metode.hamim.challenge;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;

import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.cek_hafalan.ItemModel;

/**
 * Handles the binding of data to challenge list items
 * Manages the visual state and layout configuration for each challenge item
 */
public class ChallengeItemBinder {

    private final Context context;
    private final List<ItemModel> itemList;
    private final Bitmap ayatButtonBitmap;
    private final Bitmap sambungAyatButtonBitmap;

    public ChallengeItemBinder(Context context, List<ItemModel> itemList,
                              Bitmap ayatButtonBitmap, Bitmap sambungAyatButtonBitmap) {
        this.context = context;
        this.itemList = itemList;
        this.ayatButtonBitmap = ayatButtonBitmap;
        this.sambungAyatButtonBitmap = sambungAyatButtonBitmap;
    }

    /**
     * Bind item data to the view holder
     */
    public void bindItem(@NonNull ChallengeViewHolder holder, ItemModel item, int position,
                        String title, String jmlAyat, int isHafal, String id_surat, String id_detail_surat,
                        OnItemClickListener clickListener) {

        String number = Integer.toString(position + 1);
        boolean isEven = position % 2 == 0;
        boolean shouldEnable = shouldEnableItem(position);
        boolean isSlicingLevel = isSlicingLevel(position);

        // Reset view state to ensure proper display
        holder.resetViewState();

        if (isEven) {
            setupLeftLayout(holder, number, title, isSlicingLevel, isHafal, shouldEnable,
                          item, position, clickListener);
        } else {
            setupRightLayout(holder, number, title, isSlicingLevel, isHafal, shouldEnable,
                           item, position, clickListener);
        }
    }

    /**
     * Setup left side layout (even positions)
     */
    private void setupLeftLayout(@NonNull ChallengeViewHolder holder, String number, String title,
                                boolean isSlicingLevel, int isHafal, boolean shouldEnable,
                                ItemModel item, int position, OnItemClickListener clickListener) {

        holder.layout_right.setVisibility(View.GONE);

        if (isSlicingLevel) {
            setupSlicingLevel(holder, number, title, true, isHafal, sambungAyatButtonBitmap);
        } else {
            setupNormalLevel(holder, number, title, true, isHafal, ayatButtonBitmap);
        }

        setupButtonClickListener(holder.btn, shouldEnable, isSlicingLevel, title, position,
                               item, clickListener);
        setupHafalClickListener(holder.tandai_hafal_left, shouldEnable, position, clickListener);

        holder.tandai_hafal_left.setAlpha(shouldEnable ? 1.0f : 0.5f);
    }

    /**
     * Setup right side layout (odd positions)
     */
    private void setupRightLayout(@NonNull ChallengeViewHolder holder, String number, String title,
                                 boolean isSlicingLevel, int isHafal, boolean shouldEnable,
                                 ItemModel item, int position, OnItemClickListener clickListener) {

        holder.layout_left.setVisibility(View.GONE);

        if (isSlicingLevel) {
            setupSlicingLevel(holder, number, title, false, isHafal, sambungAyatButtonBitmap);
        } else {
            setupNormalLevel(holder, number, title, false, isHafal, ayatButtonBitmap);
        }

        setupButtonClickListener(holder.btn2, shouldEnable, isSlicingLevel, title, position,
                               item, clickListener);
        setupHafalClickListener(holder.tandai_hafal_right, shouldEnable, position, clickListener);

        holder.tandai_hafal_right.setAlpha(shouldEnable ? 1.0f : 0.5f);
    }

    /**
     * Setup slicing level (Sambung Ayat) configuration
     */
    private void setupSlicingLevel(@NonNull ChallengeViewHolder holder, String number, String title,
                                  boolean isLeft, int isHafal, Bitmap buttonBitmap) {
        if (isLeft) {
            holder.urutanAudio.setText("Audio " + number);
            holder.ayat.setText("Sambung " + title);
            setupButtonImage(holder.btn, buttonBitmap, R.drawable.circle_orange);
            holder.ic_tandai_hafal_right.setVisibility(View.GONE);
        } else {
            holder.urutanAudio2.setText("Audio " + number);
            holder.ayat2.setText("Sambung " + title);
            setupButtonImage(holder.btn2, buttonBitmap, R.drawable.circle_orange);
            holder.ic_tandai_hafal_left.setVisibility(View.GONE);
        }
        updateHafalStatus(holder, isLeft, isHafal);
    }

    /**
     * Setup normal level configuration
     */
    private void setupNormalLevel(@NonNull ChallengeViewHolder holder, String number, String title,
                                 boolean isLeft, int isHafal, Bitmap buttonBitmap) {
        if (isLeft) {
            holder.urutanAudio.setText("Audio " + number);
            holder.ayat.setText(title);
            setupButtonImage(holder.btn, buttonBitmap, R.drawable.circle_orange);
            holder.ic_tandai_hafal_right.setVisibility(View.GONE);
        } else {
            holder.urutanAudio2.setText("Audio " + number);
            holder.ayat2.setText(title);
            setupButtonImage(holder.btn2, buttonBitmap, R.drawable.circle_orange);
            holder.ic_tandai_hafal_left.setVisibility(View.GONE);
        }
        updateHafalStatus(holder, isLeft, isHafal);
    }

    /**
     * Setup button image with fallback - ensure button is always visible
     */
    private void setupButtonImage(android.widget.ImageView button, Bitmap bitmap, int fallbackResource) {
        // Ensure button is visible
        button.setVisibility(View.VISIBLE);

        // Set image
        if (bitmap != null) {
            button.setImageBitmap(bitmap);
        } else {
            button.setImageResource(fallbackResource);
        }

        // Add pressed effect
        setupButtonPressedEffect(button);
    }

    /**
     * Setup pressed effect for button
     */
    private void setupButtonPressedEffect(android.widget.ImageView button) {
        // Option 1: Ripple effect (for API 21+)
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            button.setBackground(context.getDrawable(R.drawable.button_ripple_effect));
        } else {
            // Fallback for older versions
            button.setBackground(context.getDrawable(R.drawable.button_pressed_selector));
        }

        // Option 2: Scale animation on touch with smooth interpolators
        button.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case android.view.MotionEvent.ACTION_DOWN:
                    // Add haptic feedback
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        v.performHapticFeedback(android.view.HapticFeedbackConstants.VIRTUAL_KEY);
                    }

                    // Scale down when pressed with bounce effect
                    v.animate()
                        .scaleX(0.9f)
                        .scaleY(0.9f)
                        .alpha(0.8f)
                        .setDuration(120)
                        .setInterpolator(new android.view.animation.DecelerateInterpolator())
                        .start();
                    break;

                case android.view.MotionEvent.ACTION_UP:
                case android.view.MotionEvent.ACTION_CANCEL:
                    // Scale back to normal with overshoot
                    v.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .alpha(1.0f)
                        .setDuration(200)
                        .setInterpolator(new android.view.animation.OvershootInterpolator(1.5f))
                        .start();
                    break;
            }
            return false; // Don't consume the event, let onClick work
        });
    }

    /**
     * Setup button click listener
     */
    private void setupButtonClickListener(View button, boolean shouldEnable, boolean isSlicingLevel,
                                        String title, int position, ItemModel item,
                                        OnItemClickListener clickListener) {
        button.setEnabled(shouldEnable);
        button.setAlpha(shouldEnable ? 1.0f : 0.5f);

        button.setOnClickListener(v -> {
            if (shouldEnable) {
                if (isSlicingLevel) {
                    clickListener.onSambungAyatClick(title, position);
                } else {
                    clickListener.onAudioClick(item, position);
                }
            } else {
                Toast.makeText(context, "Selesaikan dulu hafalan sebelumnya", Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * Setup hafal click listener
     */
    private void setupHafalClickListener(View hafalButton, boolean shouldEnable, int position,
                                       OnItemClickListener clickListener) {
        // Add pressed effect for hafal button
        setupHafalButtonPressedEffect(hafalButton);

        if (!shouldEnable) {
            hafalButton.setOnClickListener(v ->
                Toast.makeText(context, "Selesaikan dulu hafalan sebelumnya", Toast.LENGTH_SHORT).show());
        } else {
            hafalButton.setOnClickListener(v -> clickListener.onHafalClick(position));
        }
    }

    /**
     * Setup pressed effect for hafal button
     */
    private void setupHafalButtonPressedEffect(View hafalButton) {
        // Add ripple background effect
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
            hafalButton.setBackground(context.getDrawable(R.drawable.hafal_button_ripple));
        }

        // Add scale animation
        hafalButton.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case android.view.MotionEvent.ACTION_DOWN:
                    // Add haptic feedback
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        v.performHapticFeedback(android.view.HapticFeedbackConstants.VIRTUAL_KEY);
                    }

                    // Scale down and change alpha when pressed
                    v.animate()
                        .scaleX(0.95f)
                        .scaleY(0.95f)
                        .alpha(0.8f)
                        .setDuration(100)
                        .setInterpolator(new android.view.animation.DecelerateInterpolator())
                        .start();
                    break;

                case android.view.MotionEvent.ACTION_UP:
                case android.view.MotionEvent.ACTION_CANCEL:
                    // Scale back to normal and restore alpha
                    v.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .alpha(1.0f)
                        .setDuration(150)
                        .setInterpolator(new android.view.animation.OvershootInterpolator(1.2f))
                        .start();
                    break;
            }
            return false; // Don't consume the event, let onClick work
        });
    }

    /**
     * Update hafal status display
     */
    public void updateHafalStatus(@NonNull ChallengeViewHolder holder, boolean isLeft, int isHafal) {
        int activeDrawable = isLeft ? R.drawable.left_active : R.drawable.right_active;
        int nonActiveDrawable = isLeft ? R.drawable.left_non_active : R.drawable.right_non_active;
        int checkDrawable = R.drawable.ic_check;
        int uncheckDrawable = R.drawable.ic_uncheck_new;
        String hafalText = " Sudah Hafal";
        String tandaiHafalText = " Tandai Hafal";

        if (isHafal == 100) {
            holder.circle.setImageResource(activeDrawable);
            (isLeft ? holder.ic_tandai_hafal_left : holder.ic_tandai_hafal_right).setImageResource(checkDrawable);
            (isLeft ? holder.text_hafal_left : holder.text_hafal_right).setText(hafalText);
        } else {
            holder.circle.setImageResource(nonActiveDrawable);
            (isLeft ? holder.ic_tandai_hafal_left : holder.ic_tandai_hafal_right).setImageResource(uncheckDrawable);
            (isLeft ? holder.text_hafal_left : holder.text_hafal_right).setText(tandaiHafalText);
        }
    }

    /**
     * Check if item should be enabled based on previous completions
     */
    private boolean shouldEnableItem(int position) {
        for (int i = 0; i < position; i++) {
            if (itemList.get(i).getIs_hafal() != 100) {
                return false;
            }
        }
        return true;
    }

    /**
     * Check if position is a slicing level (Sambung Ayat)
     */
    private boolean isSlicingLevel(int position) {
        return (position == 1 || position == 4 || position == 7);
    }

    /**
     * Interface for handling item clicks
     */
    public interface OnItemClickListener {
        void onAudioClick(ItemModel item, int position);
        void onSambungAyatClick(String title, int position);
        void onHafalClick(int position);
    }
}
