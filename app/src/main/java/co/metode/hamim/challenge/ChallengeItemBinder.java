package co.metode.hamim.challenge;

import android.content.Context;
import android.graphics.Bitmap;
import android.view.View;
import android.widget.Toast;

import androidx.annotation.NonNull;

import java.util.List;

import co.metode.hamim.R;
import co.metode.hamim.cek_hafalan.ItemModel;

/**
 * Handles the binding of data to challenge list items
 * Manages the visual state and layout configuration for each challenge item
 */
public class ChallengeItemBinder {

    private final Context context;
    private final List<ItemModel> itemList;
    private final Bitmap ayatButtonBitmap;
    private final Bitmap sambungAyatButtonBitmap;

    public ChallengeItemBinder(Context context, List<ItemModel> itemList,
                              Bitmap ayatButtonBitmap, Bitmap sambungAyatButtonBitmap) {
        this.context = context;
        this.itemList = itemList;
        this.ayatButtonBitmap = ayatButtonBitmap;
        this.sambungAyatButtonBitmap = sambungAyatButtonBitmap;
    }

    /**
     * Bind item data to the view holder
     */
    public void bindItem(@NonNull ChallengeViewHolder holder, ItemModel item, int position,
                        String title, String jmlAyat, int isHafal, String id_surat, String id_detail_surat,
                        OnItemClickListener clickListener) {

        String number = Integer.toString(position + 1);
        boolean isEven = position % 2 == 0;
        boolean shouldEnable = shouldEnableItem(position);
        boolean isSlicingLevel = isSlicingLevel(position);

        // Reset view state only when necessary
        // holder.resetViewState(); // Commented out for performance - let individual setups handle state

        if (isEven) {
            setupLeftLayout(holder, number, title, isSlicingLevel, isHafal, shouldEnable,
                          item, position, clickListener);
        } else {
            setupRightLayout(holder, number, title, isSlicingLevel, isHafal, shouldEnable,
                           item, position, clickListener);
        }
    }

    /**
     * Setup left side layout (even positions)
     */
    private void setupLeftLayout(@NonNull ChallengeViewHolder holder, String number, String title,
                                boolean isSlicingLevel, int isHafal, boolean shouldEnable,
                                ItemModel item, int position, OnItemClickListener clickListener) {

        holder.layout_right.setVisibility(View.GONE);

        if (isSlicingLevel) {
            setupSlicingLevel(holder, number, title, true, isHafal, sambungAyatButtonBitmap);
        } else {
            setupNormalLevel(holder, number, title, true, isHafal, ayatButtonBitmap);
        }

        setupButtonClickListener(holder.btn, shouldEnable, isSlicingLevel, title, position,
                               item, clickListener);
        setupHafalClickListener(holder.tandai_hafal_left, shouldEnable, position, clickListener);

        holder.tandai_hafal_left.setAlpha(shouldEnable ? 1.0f : 0.5f);
    }

    /**
     * Setup right side layout (odd positions)
     */
    private void setupRightLayout(@NonNull ChallengeViewHolder holder, String number, String title,
                                 boolean isSlicingLevel, int isHafal, boolean shouldEnable,
                                 ItemModel item, int position, OnItemClickListener clickListener) {

        holder.layout_left.setVisibility(View.GONE);

        if (isSlicingLevel) {
            setupSlicingLevel(holder, number, title, false, isHafal, sambungAyatButtonBitmap);
        } else {
            setupNormalLevel(holder, number, title, false, isHafal, ayatButtonBitmap);
        }

        setupButtonClickListener(holder.btn2, shouldEnable, isSlicingLevel, title, position,
                               item, clickListener);
        setupHafalClickListener(holder.tandai_hafal_right, shouldEnable, position, clickListener);

        holder.tandai_hafal_right.setAlpha(shouldEnable ? 1.0f : 0.5f);
    }

    /**
     * Setup slicing level (Sambung Ayat) configuration
     */
    private void setupSlicingLevel(@NonNull ChallengeViewHolder holder, String number, String title,
                                  boolean isLeft, int isHafal, Bitmap buttonBitmap) {
        if (isLeft) {
            holder.urutanAudio.setText("Audio " + number);
            holder.ayat.setText("Sambung " + title);
            setupButtonImage(holder.btn, buttonBitmap, R.drawable.circle_orange);
            holder.ic_tandai_hafal_right.setVisibility(View.GONE);
        } else {
            holder.urutanAudio2.setText("Audio " + number);
            holder.ayat2.setText("Sambung " + title);
            setupButtonImage(holder.btn2, buttonBitmap, R.drawable.circle_orange);
            holder.ic_tandai_hafal_left.setVisibility(View.GONE);
        }
        updateHafalStatus(holder, isLeft, isHafal);
    }

    /**
     * Setup normal level configuration
     */
    private void setupNormalLevel(@NonNull ChallengeViewHolder holder, String number, String title,
                                 boolean isLeft, int isHafal, Bitmap buttonBitmap) {
        if (isLeft) {
            holder.urutanAudio.setText("Audio " + number);
            holder.ayat.setText(title);
            setupButtonImage(holder.btn, buttonBitmap, R.drawable.circle_orange);
            holder.ic_tandai_hafal_right.setVisibility(View.GONE);
        } else {
            holder.urutanAudio2.setText("Audio " + number);
            holder.ayat2.setText(title);
            setupButtonImage(holder.btn2, buttonBitmap, R.drawable.circle_orange);
            holder.ic_tandai_hafal_left.setVisibility(View.GONE);
        }
        updateHafalStatus(holder, isLeft, isHafal);
    }

    /**
     * Setup button image with fallback
     */
    private void setupButtonImage(android.widget.ImageView button, Bitmap bitmap, int fallbackResource) {
        if (bitmap != null) {
            button.setImageBitmap(bitmap);
        } else {
            button.setImageResource(fallbackResource);
        }
    }

    /**
     * Setup button click listener
     */
    private void setupButtonClickListener(View button, boolean shouldEnable, boolean isSlicingLevel,
                                        String title, int position, ItemModel item,
                                        OnItemClickListener clickListener) {
        button.setEnabled(shouldEnable);
        button.setAlpha(shouldEnable ? 1.0f : 0.5f);

        button.setOnClickListener(v -> {
            if (shouldEnable) {
                if (isSlicingLevel) {
                    clickListener.onSambungAyatClick(title, position);
                } else {
                    clickListener.onAudioClick(item, position);
                }
            } else {
                Toast.makeText(context, "Selesaikan dulu hafalan sebelumnya", Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * Setup hafal click listener
     */
    private void setupHafalClickListener(View hafalButton, boolean shouldEnable, int position,
                                       OnItemClickListener clickListener) {
        if (!shouldEnable) {
            hafalButton.setOnClickListener(v ->
                Toast.makeText(context, "Selesaikan dulu hafalan sebelumnya", Toast.LENGTH_SHORT).show());
        } else {
            hafalButton.setOnClickListener(v -> clickListener.onHafalClick(position));
        }
    }

    /**
     * Update hafal status display
     */
    public void updateHafalStatus(@NonNull ChallengeViewHolder holder, boolean isLeft, int isHafal) {
        int activeDrawable = isLeft ? R.drawable.left_active : R.drawable.right_active;
        int nonActiveDrawable = isLeft ? R.drawable.left_non_active : R.drawable.right_non_active;
        int checkDrawable = R.drawable.ic_check;
        int uncheckDrawable = R.drawable.ic_uncheck_new;
        String hafalText = " Sudah Hafal";
        String tandaiHafalText = " Tandai Hafal";

        if (isHafal == 100) {
            holder.circle.setImageResource(activeDrawable);
            (isLeft ? holder.ic_tandai_hafal_left : holder.ic_tandai_hafal_right).setImageResource(checkDrawable);
            (isLeft ? holder.text_hafal_left : holder.text_hafal_right).setText(hafalText);
        } else {
            holder.circle.setImageResource(nonActiveDrawable);
            (isLeft ? holder.ic_tandai_hafal_left : holder.ic_tandai_hafal_right).setImageResource(uncheckDrawable);
            (isLeft ? holder.text_hafal_left : holder.text_hafal_right).setText(tandaiHafalText);
        }
    }

    /**
     * Check if item should be enabled based on previous completions
     */
    private boolean shouldEnableItem(int position) {
        for (int i = 0; i < position; i++) {
            if (itemList.get(i).getIs_hafal() != 100) {
                return false;
            }
        }
        return true;
    }

    /**
     * Check if position is a slicing level (Sambung Ayat)
     */
    private boolean isSlicingLevel(int position) {
        return (position == 1 || position == 4 || position == 7);
    }

    /**
     * Interface for handling item clicks
     */
    public interface OnItemClickListener {
        void onAudioClick(ItemModel item, int position);
        void onSambungAyatClick(String title, int position);
        void onHafalClick(int position);
    }
}
