package co.metode.hamim.challenge;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;

import androidx.core.content.ContextCompat;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import co.metode.hamim.R;

/**
 * Custom view that creates sparkle effect around buttons
 * Uses app theme colors (orange variations) for cohesive design
 */
public class SparkleEffectView extends View {

    private List<Sparkle> sparkles;
    private Paint paint;
    private Random random;
    private Runnable animationRunnable;
    private boolean isAnimating = false;

    // Theme colors for sparkles
    private int[] themeColors;

    // Animation properties
    private static final int SPARKLE_COUNT = 12;
    private static final float SPARKLE_RADIUS = 45f; // Closer to button edge (button radius ~30dp)
    private static final int FRAME_RATE = 60; // 60 FPS
    private static final long FRAME_DELAY = 1000 / FRAME_RATE; // ~16ms per frame
    private static final float ROTATION_SPEED_MULTIPLIER = 60f;

    // Time tracking for smooth infinite animation
    private long startTime = 0;

    public SparkleEffectView(Context context) {
        super(context);
        init();
    }

    public SparkleEffectView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        sparkles = new ArrayList<>();
        paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        random = new Random();

        // Initialize theme colors - vibrant and visible colors
        themeColors = new int[] {
            0xFFFF6B35, // Vibrant orange
            0xFFFF8E53, // Light vibrant orange
            0xFFFFD23F, // Bright yellow
            0xFF06FFA5, // Bright mint green
            0xFF4ECDC4, // Turquoise
            0xFF45B7D1, // Sky blue
            0xFF96CEB4, // Sage green
            0xFFFECE2F, // Golden yellow
            0xFFFF9FF3, // Pink
            0xFFBB86FC  // Purple
        };

        initializeSparkles();
        setupAnimation();
    }

    private void initializeSparkles() {
        sparkles.clear();
        for (int i = 0; i < SPARKLE_COUNT; i++) {
            sparkles.add(new Sparkle());
        }
    }

    private void setupAnimation() {
        animationRunnable = new Runnable() {
            @Override
            public void run() {
                if (isAnimating) {
                    updateSparkles();
                    invalidate();
                    postDelayed(this, FRAME_DELAY);
                }
            }
        };
    }

    private void updateSparkles() {
        float centerX = getWidth() / 2f;
        float centerY = getHeight() / 2f;

        // Calculate elapsed time since animation started
        long currentTime = System.currentTimeMillis();
        if (startTime == 0) {
            startTime = currentTime;
        }
        long elapsedTime = currentTime - startTime;

        for (Sparkle sparkle : sparkles) {
            sparkle.update(elapsedTime, centerX, centerY);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if (sparkles == null) return;

        for (Sparkle sparkle : sparkles) {
            if (sparkle.isVisible()) {
                sparkle.draw(canvas, paint);
            }
        }
    }

    public void startSparkle() {
        if (!isAnimating) {
            setVisibility(VISIBLE);
            isAnimating = true;
            startTime = System.currentTimeMillis(); // Reset start time
            post(animationRunnable);
        }
    }

    public void stopSparkle() {
        if (isAnimating) {
            isAnimating = false;
            removeCallbacks(animationRunnable);
            setVisibility(GONE);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopSparkle(); // Clean stop when view is detached
    }

    /**
     * Individual sparkle particle
     */
    private class Sparkle {
        private float x, y;
        private float size;
        private float alpha;
        private int color;
        private float angle;
        private float speed;
        private float phase;
        private Path starPath;

        public Sparkle() {
            reset();
            createStarPath();
        }

        private void reset() {
            angle = random.nextFloat() * 360f;
            speed = 0.8f + random.nextFloat() * 0.6f; // Slower, more consistent speed (0.8-1.4)
            phase = random.nextFloat() * 2f * (float) Math.PI; // Random start phase
            color = themeColors[random.nextInt(themeColors.length)];
            size = 6f + random.nextFloat() * 6f; // Bigger sparkles (6-12px)
        }

        private void createStarPath() {
            starPath = new Path();
            float radius = size;
            float innerRadius = radius * 0.4f;

            // Create 4-point star
            for (int i = 0; i < 8; i++) {
                float currentRadius = (i % 2 == 0) ? radius : innerRadius;
                float currentAngle = (float) (i * Math.PI / 4);
                float x = (float) (currentRadius * Math.cos(currentAngle));
                float y = (float) (currentRadius * Math.sin(currentAngle));

                if (i == 0) {
                    starPath.moveTo(x, y);
                } else {
                    starPath.lineTo(x, y);
                }
            }
            starPath.close();
        }

        public void update(long elapsedTimeMs, float centerX, float centerY) {
            // Convert elapsed time to smooth rotation (no cycles, no jeda!)
            float timeInSeconds = elapsedTimeMs / 1000f;

            // Calculate position in orbit around center - smooth continuous rotation
            float currentAngle = angle + (timeInSeconds * speed * ROTATION_SPEED_MULTIPLIER);

            // Vary radius slightly but stay closer to button edge
            float radiusVariation = (float) Math.sin(phase + timeInSeconds * 2f) * 8f;
            float radius = SPARKLE_RADIUS + radiusVariation;

            x = centerX + (float) (radius * Math.cos(Math.toRadians(currentAngle)));
            y = centerY + (float) (radius * Math.sin(Math.toRadians(currentAngle)));

            // Pulsing alpha for twinkling effect - much more visible
            alpha = 0.7f + 0.3f * (float) Math.abs(Math.sin(phase + timeInSeconds * 3f));

            // Vary size slightly - bigger sparkles
            float sizeMultiplier = 1.0f + 0.3f * (float) Math.abs(Math.sin(phase + timeInSeconds * 4f));
            size = (6f + random.nextFloat() * 3f) * sizeMultiplier;
        }

        public boolean isVisible() {
            return alpha > 0.5f; // Higher threshold for visibility
        }

        public void draw(Canvas canvas, Paint paint) {
            // Draw main star with full opacity
            paint.setColor(color);
            paint.setAlpha((int) (255 * alpha));
            paint.setStyle(Paint.Style.FILL);

            canvas.save();
            canvas.translate(x, y);
            canvas.scale(size / 6f, size / 6f); // Bigger scale for star
            canvas.drawPath(starPath, paint);
            canvas.restore();

            // Add bright glow effect
            paint.setAlpha((int) (180 * alpha)); // Higher glow opacity
            paint.setStyle(Paint.Style.STROKE);
            paint.setStrokeWidth(3f); // Thicker glow
            canvas.drawCircle(x, y, size * 1.8f, paint);

            // Add outer glow
            paint.setAlpha((int) (120 * alpha));
            paint.setStrokeWidth(1.5f);
            canvas.drawCircle(x, y, size * 2.2f, paint);
        }
    }
}
