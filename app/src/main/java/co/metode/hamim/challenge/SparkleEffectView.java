package co.metode.hamim.challenge;

import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Path;
import android.util.AttributeSet;
import android.view.View;
import android.view.animation.LinearInterpolator;

import androidx.core.content.ContextCompat;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import co.metode.hamim.R;

/**
 * Custom view that creates sparkle effect around buttons
 * Uses app theme colors (orange variations) for cohesive design
 */
public class SparkleEffectView extends View {

    private List<Sparkle> sparkles;
    private Paint paint;
    private Random random;
    private ValueAnimator animator;

    // Theme colors for sparkles
    private int[] themeColors;

    // Animation properties
    private static final int SPARKLE_COUNT = 12;
    private static final long ANIMATION_DURATION = 2000;
    private static final float SPARKLE_RADIUS = 80f; // Distance from center

    public SparkleEffectView(Context context) {
        super(context);
        init();
    }

    public SparkleEffectView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        sparkles = new ArrayList<>();
        paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        random = new Random();

        // Initialize theme colors (orange variations from app theme)
        themeColors = new int[] {
            ContextCompat.getColor(getContext(), R.color.orangemuda),     // #F58634
            ContextCompat.getColor(getContext(), R.color.orangetua),      // #BB4217
            ContextCompat.getColor(getContext(), R.color.orangetomerah),  // #AA2A00
            ContextCompat.getColor(getContext(), R.color.warning),        // #F0B728
            ContextCompat.getColor(getContext(), R.color.yellow),         // #F4BF00
            0xFFFFB74D, // Light orange complement
            0xFFFF9800, // Orange complement
            0xFFFFC107  // Amber complement
        };

        initializeSparkles();
        setupAnimation();
    }

    private void initializeSparkles() {
        sparkles.clear();
        for (int i = 0; i < SPARKLE_COUNT; i++) {
            sparkles.add(new Sparkle());
        }
    }

    private void setupAnimation() {
        animator = ValueAnimator.ofFloat(0f, 1f);
        animator.setDuration(ANIMATION_DURATION);
        animator.setRepeatCount(ValueAnimator.INFINITE);
        animator.setInterpolator(new LinearInterpolator());
        animator.addUpdateListener(animation -> {
            updateSparkles((Float) animation.getAnimatedValue());
            invalidate();
        });
    }

    private void updateSparkles(float animationProgress) {
        float centerX = getWidth() / 2f;
        float centerY = getHeight() / 2f;

        for (Sparkle sparkle : sparkles) {
            sparkle.update(animationProgress, centerX, centerY);
        }
    }

    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);

        if (sparkles == null) return;

        for (Sparkle sparkle : sparkles) {
            if (sparkle.isVisible()) {
                sparkle.draw(canvas, paint);
            }
        }
    }

    public void startSparkle() {
        if (animator != null && !animator.isRunning()) {
            setVisibility(VISIBLE);
            animator.start();
        }
    }

    public void stopSparkle() {
        if (animator != null && animator.isRunning()) {
            animator.cancel();
            setVisibility(GONE);
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        if (animator != null) {
            animator.cancel();
        }
    }

    /**
     * Individual sparkle particle
     */
    private class Sparkle {
        private float x, y;
        private float size;
        private float alpha;
        private int color;
        private float angle;
        private float speed;
        private float phase;
        private Path starPath;

        public Sparkle() {
            reset();
            createStarPath();
        }

        private void reset() {
            angle = random.nextFloat() * 360f;
            speed = 0.5f + random.nextFloat() * 1.5f; // Vary speed
            phase = random.nextFloat() * 2f * (float) Math.PI; // Random start phase
            color = themeColors[random.nextInt(themeColors.length)];
            size = 3f + random.nextFloat() * 5f; // Vary size
        }

        private void createStarPath() {
            starPath = new Path();
            float radius = size;
            float innerRadius = radius * 0.4f;

            // Create 4-point star
            for (int i = 0; i < 8; i++) {
                float currentRadius = (i % 2 == 0) ? radius : innerRadius;
                float currentAngle = (float) (i * Math.PI / 4);
                float x = (float) (currentRadius * Math.cos(currentAngle));
                float y = (float) (currentRadius * Math.sin(currentAngle));

                if (i == 0) {
                    starPath.moveTo(x, y);
                } else {
                    starPath.lineTo(x, y);
                }
            }
            starPath.close();
        }

        public void update(float animationProgress, float centerX, float centerY) {
            // Calculate position in orbit around center
            float currentAngle = angle + (animationProgress * 360f * speed);
            float radius = SPARKLE_RADIUS + (float) Math.sin(phase + animationProgress * 4 * Math.PI) * 20f;

            x = centerX + (float) (radius * Math.cos(Math.toRadians(currentAngle)));
            y = centerY + (float) (radius * Math.sin(Math.toRadians(currentAngle)));

            // Pulsing alpha for twinkling effect
            alpha = 0.3f + 0.7f * (float) Math.abs(Math.sin(phase + animationProgress * 6 * Math.PI));

            // Vary size slightly
            float sizeMultiplier = 0.8f + 0.4f * (float) Math.abs(Math.sin(phase + animationProgress * 8 * Math.PI));
            size = (3f + random.nextFloat() * 3f) * sizeMultiplier;
        }

        public boolean isVisible() {
            return alpha > 0.1f;
        }

        public void draw(Canvas canvas, Paint paint) {
            paint.setColor(color);
            paint.setAlpha((int) (255 * alpha));
            paint.setStyle(Paint.Style.FILL);

            canvas.save();
            canvas.translate(x, y);
            canvas.scale(size / 8f, size / 8f); // Scale the star
            canvas.drawPath(starPath, paint);
            canvas.restore();

            // Add glow effect
            paint.setAlpha((int) (100 * alpha));
            paint.setStyle(Paint.Style.STROKE);
            paint.setStrokeWidth(2f);
            canvas.drawCircle(x, y, size * 1.5f, paint);
        }
    }
}
