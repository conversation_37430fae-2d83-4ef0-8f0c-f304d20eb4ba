package co.metode.hamim.challenge;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import co.metode.hamim.R;

/**
 * ViewHolder class for Challenge RecyclerView items
 * Handles the view binding and initialization for challenge list items
 */
public class ChallengeViewHolder extends RecyclerView.ViewHolder {

    // Button ImageViews
    public ImageView btn, btn2;

    // Text Views
    public TextView urutanAudio, urutanAudio2, ayat, ayat2, text_hafal_left, text_hafal_right;

    // Icon ImageViews
    public ImageView circle, ic_tandai_hafal_left, ic_tandai_hafal_right;

    // Layout containers
    public LinearLayout layout_left, layout_right, tandai_hafal_left, tandai_hafal_right;

    // Sparkle effect views
    public SparkleEffectView sparkle_left, sparkle_right;

    // Status field
    public String complete;

    public ChallengeViewHolder(@NonNull View itemView) {
        super(itemView);
        initializeViews(itemView);
        complete = "0";
    }

    /**
     * Initialize all view components
     */
    private void initializeViews(View itemView) {
        // Button views
        btn = itemView.findViewById(R.id.btn_surat);
        btn2 = itemView.findViewById(R.id.btn_surat2);

        // Background circle
        circle = itemView.findViewById(R.id.circle);

        // Layout containers
        layout_right = itemView.findViewById(R.id.layout_right);
        layout_left = itemView.findViewById(R.id.layout_left);

        // Text views
        urutanAudio = itemView.findViewById(R.id.urutan_audio);
        urutanAudio2 = itemView.findViewById(R.id.urutan_audio2);
        ayat = itemView.findViewById(R.id.ayat);
        ayat2 = itemView.findViewById(R.id.ayat2);
        text_hafal_left = itemView.findViewById(R.id.text_hafal_left);
        text_hafal_right = itemView.findViewById(R.id.text_hafal_right);

        // Icon views
        ic_tandai_hafal_left = itemView.findViewById(R.id.ic_tandai_hafal_left);
        ic_tandai_hafal_right = itemView.findViewById(R.id.ic_tandai_hafal_right);

        // Clickable layouts
        tandai_hafal_left = itemView.findViewById(R.id.tandai_hafal_left);
        tandai_hafal_right = itemView.findViewById(R.id.tandai_hafal_right);

        // Sparkle effect views
        sparkle_left = itemView.findViewById(R.id.sparkle_left);
        sparkle_right = itemView.findViewById(R.id.sparkle_right);
    }

    /**
     * Reset view state for recycling - ensure all views are properly reset
     */
    public void resetViewState() {
        // Reset visibility - both layouts should be visible initially
        layout_left.setVisibility(View.VISIBLE);
        layout_right.setVisibility(View.VISIBLE);

        // Reset alpha to default
        btn.setAlpha(1.0f);
        btn2.setAlpha(1.0f);
        tandai_hafal_left.setAlpha(1.0f);
        tandai_hafal_right.setAlpha(1.0f);

        // Reset enabled state to default
        btn.setEnabled(true);
        btn2.setEnabled(true);
        tandai_hafal_left.setEnabled(true);
        tandai_hafal_right.setEnabled(true);

        // Reset icon visibility to default (visible)
        ic_tandai_hafal_left.setVisibility(View.VISIBLE);
        ic_tandai_hafal_right.setVisibility(View.VISIBLE);

        // Reset button visibility to default (visible)
        btn.setVisibility(View.VISIBLE);
        btn2.setVisibility(View.VISIBLE);

        // Reset sparkle effects
        if (sparkle_left != null) {
            sparkle_left.stopSparkle();
        }
        if (sparkle_right != null) {
            sparkle_right.stopSparkle();
        }

        // Clear text content to avoid recycling issues
        urutanAudio.setText("");
        urutanAudio2.setText("");
        ayat.setText("");
        ayat2.setText("");
        text_hafal_left.setText("");
        text_hafal_right.setText("");
    }
}
