package co.metode.hamim.challenge;

import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import co.metode.hamim.R;

/**
 * ViewHolder class for Challenge RecyclerView items
 * Handles the view binding and initialization for challenge list items
 */
public class ChallengeViewHolder extends RecyclerView.ViewHolder {

    // Button ImageViews
    public ImageView btn, btn2;

    // Text Views
    public TextView urutanAudio, urutanAudio2, ayat, ayat2, text_hafal_left, text_hafal_right;

    // Icon ImageViews
    public ImageView circle, ic_tandai_hafal_left, ic_tandai_hafal_right;

    // Layout containers
    public LinearLayout layout_left, layout_right, tandai_hafal_left, tandai_hafal_right;

    // Status field
    public String complete;

    public ChallengeViewHolder(@NonNull View itemView) {
        super(itemView);
        initializeViews(itemView);
        complete = "0";
    }

    /**
     * Initialize all view components
     */
    private void initializeViews(View itemView) {
        // Button views
        btn = itemView.findViewById(R.id.btn_surat);
        btn2 = itemView.findViewById(R.id.btn_surat2);

        // Background circle
        circle = itemView.findViewById(R.id.circle);

        // Layout containers
        layout_right = itemView.findViewById(R.id.layout_right);
        layout_left = itemView.findViewById(R.id.layout_left);

        // Text views
        urutanAudio = itemView.findViewById(R.id.urutan_audio);
        urutanAudio2 = itemView.findViewById(R.id.urutan_audio2);
        ayat = itemView.findViewById(R.id.ayat);
        ayat2 = itemView.findViewById(R.id.ayat2);
        text_hafal_left = itemView.findViewById(R.id.text_hafal_left);
        text_hafal_right = itemView.findViewById(R.id.text_hafal_right);

        // Icon views
        ic_tandai_hafal_left = itemView.findViewById(R.id.ic_tandai_hafal_left);
        ic_tandai_hafal_right = itemView.findViewById(R.id.ic_tandai_hafal_right);

        // Clickable layouts
        tandai_hafal_left = itemView.findViewById(R.id.tandai_hafal_left);
        tandai_hafal_right = itemView.findViewById(R.id.tandai_hafal_right);
    }

    /**
     * Reset view state for recycling - optimized to avoid unnecessary operations
     */
    public void resetViewState() {
        // Only reset visibility if needed (both layouts should be visible initially)
        if (layout_left.getVisibility() != View.VISIBLE) {
            layout_left.setVisibility(View.VISIBLE);
        }
        if (layout_right.getVisibility() != View.VISIBLE) {
            layout_right.setVisibility(View.VISIBLE);
        }

        // Reset alpha only if different from default
        if (btn.getAlpha() != 1.0f) btn.setAlpha(1.0f);
        if (btn2.getAlpha() != 1.0f) btn2.setAlpha(1.0f);
        if (tandai_hafal_left.getAlpha() != 1.0f) tandai_hafal_left.setAlpha(1.0f);
        if (tandai_hafal_right.getAlpha() != 1.0f) tandai_hafal_right.setAlpha(1.0f);

        // Reset enabled state only if different from default
        if (!btn.isEnabled()) btn.setEnabled(true);
        if (!btn2.isEnabled()) btn2.setEnabled(true);
        if (!tandai_hafal_left.isEnabled()) tandai_hafal_left.setEnabled(true);
        if (!tandai_hafal_right.isEnabled()) tandai_hafal_right.setEnabled(true);

        // Text will be set by binder, no need to clear explicitly
    }
}
