package co.metode.hamim;

import android.content.Context;
import android.content.Intent;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkInfo;
import android.net.NetworkRequest;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.OvershootInterpolator;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.google.android.material.progressindicator.LinearProgressIndicator;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.cek_hafalan.ItemModel;
import co.metode.hamim.cek_hafalan.ResponseDataItem;
import co.metode.hamim.cek_hafalan.database.HafalanDatabase;
import co.metode.hamim.cek_hafalan.database.HafalanEntity;
import co.metode.hamim.challenge.AdapterChallenge;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class AudioChallenge extends AppCompatActivity implements AdapterChallenge.OnHafalStatusChangedListener {

    private Context context;
    private RecyclerView recyclerView;
    private TextView perjalanan;
    private LinearLayoutManager linearLayoutManager;
    private AdapterChallenge adapter;
    private Intent intent;
    private String idJuz;
    private String idUser;
    private String surat;
    private TextView tvBack;
    private SwipeRefreshLayout refreshSurat;
    private LinearLayout layout;
    private int countHafal = 0;
    private int totalSurat = 0;
    private int scrollY = 0;
    private TextView tvProgressPercent;
    private LinearProgressIndicator progressHafalan;

    private HafalanDatabase database;
    private ExecutorService executorService;
    private volatile boolean isActivityDestroyed = false;

    private final Handler handler = new Handler();
    private ConnectivityManager.NetworkCallback networkCallback;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_audio_challenge);

        // Inisialisasi database dan executor
        database = HafalanDatabase.getDatabase(this);
        executorService = Executors.newSingleThreadExecutor();

        context = this;

        // Inisialisasi komponen UI
        progressHafalan = findViewById(R.id.progressHafalan);
        tvProgressPercent = findViewById(R.id.tvProgressPercent);
        recyclerView = findViewById(R.id.rv_data);
        perjalanan = findViewById(R.id.perjalanan);
        tvBack = findViewById(R.id.tvBack);
        refreshSurat = findViewById(R.id.swipe_refresh_layout);
        layout = findViewById(R.id.shimmerAudioSurat);

        linearLayoutManager = new LinearLayoutManager(this);
        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setNestedScrollingEnabled(false);
        recyclerView.setItemViewCacheSize(20);
        recyclerView.setDrawingCacheEnabled(true);
        recyclerView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);

        // Ambil data dari Intent
        intent = getIntent();
        idJuz = intent.getStringExtra("id_juz");
        idUser = intent.getStringExtra("id_user");
        surat = intent.getStringExtra("nama_juz");


        // Set teks untuk tombol kembali atau judul halaman
        if (surat != null) {
            tvBack.setText(surat);
        } else {
            tvBack.setText("Daftar Surat");
        }

        setupRefreshListener();
        registerNetworkCallback();
        setupToolbar();
    }

    /**
     * Tombol Back
     */
    private void setupToolbar() {
        ConstraintLayout back = findViewById(R.id.back);
        back.setOnClickListener(v -> onBackPressed());
    }

    /**
     * Registrasi callback untuk mendeteksi perubahan koneksi jaringan.
     * Jika koneksi tersedia, sinkronisasi data offline.
     */
    private void registerNetworkCallback() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            networkCallback = new ConnectivityManager.NetworkCallback() {
                @Override
                public void onAvailable(Network network) {
                    runOnUiThread(() -> {
                        Log.d("AudioChallenge", "Koneksi internet tersedia, menyinkronkan data...");
                        if (adapter != null) {
                            adapter.syncOfflineActions(); // Sinkronisasi aksi offline di adapter
                        }
                    });
                }
            };

            NetworkRequest.Builder builder = new NetworkRequest.Builder();
            connectivityManager.registerNetworkCallback(builder.build(), networkCallback);
        }
    }

    /**
     * Setup listener untuk swipe refresh layout.
     * Memanggil onResume() untuk refresh data dan menampilkan animasi loading.
     */
    private void setupRefreshListener() {
        // Hanya aktifkan refresh ketika berada di posisi paling atas
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                refreshSurat.setEnabled(recyclerView.computeVerticalScrollOffset() == 0);
            }
        });

        // Listener untuk menangani refresh dengan animasi transisi
        refreshSurat.setOnRefreshListener(() -> {
            scrollY = recyclerView.computeVerticalScrollOffset();
            getDataSurat();
        });
    }

    /**
     * Animasi transisi layout saat data dimuat.
     * Menyembunyikan shimmer layout dan menampilkan RecyclerView dengan animasi.
     */
    private void animateLayoutTransition() {
        if (adapter == null || adapter.getItemCount() == 0) return; // Jangan animasi jika belum ada data

        recyclerView.setVisibility(View.VISIBLE);
        recyclerView.setAlpha(0f);
        recyclerView.setTranslationY(recyclerView.getHeight());

        layout.animate()
                .alpha(0.0f)
                .translationY(-layout.getHeight() / 2f)
                .setInterpolator(new AccelerateInterpolator())
                .setDuration(400)
                .withEndAction(() -> layout.setVisibility(View.GONE));

        recyclerView.animate()
                .alpha(1.0f)
                .translationY(0)
                .setInterpolator(new OvershootInterpolator(1.2f))
                .setStartDelay(200)
                .setDuration(700);
    }


    /**
     * Mengambil data surat dari API atau database lokal.
     * Jika ada koneksi internet, ambil data dari API dan simpan ke database.
     * Jika tidak ada koneksi, ambil data dari database lokal.
     */
    private void getDataSurat() {
        if (idUser == null) {
            Toast.makeText(context, "Data idUser tidak ditemukan", Toast.LENGTH_SHORT).show();
            return;
        }

        if (isNetworkAvailable()) {
            // Ambil data dari API jika ada koneksi
            ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
            Call<List<ResponseDataItem>> call = apiInterface.getSurat(idJuz, Integer.parseInt(idUser));
            call.enqueue(new Callback<List<ResponseDataItem>>() {
                @Override
                public void onResponse(@NonNull Call<List<ResponseDataItem>> call, @NonNull Response<List<ResponseDataItem>> response) {
                    try {
                        if (response.isSuccessful()) {
                            List<ResponseDataItem> posts = response.body();
                            // Simpan data ke database untuk penggunaan offline
                            saveToDatabase(posts);
                            processData(posts); // Proses data dan tampilkan di RecyclerView
                        }
                    } finally {
                        refreshSurat.setRefreshing(false);
                    }

                }

                @Override
                public void onFailure(Call<List<ResponseDataItem>> call, Throwable t) {
                    try {
                        loadFromDatabase();
                    } finally {
                        refreshSurat.setRefreshing(false);
                    }
                }
            });
        } else {
            try {
                loadFromDatabase();
            } finally {
                refreshSurat.setRefreshing(false);
            }
        }
    }

    /**
     * Memproses data surat dan menampilkan di RecyclerView.
     * Menghitung jumlah surat yang sudah dihafal dan memperbarui progress bar.
     */
    private void processData(List<ResponseDataItem> posts) {
        List<ItemModel> itemList = new ArrayList<>();
        countHafal = 0;
        totalSurat = posts.size();

        for (int i = 0; i < posts.size(); i++) {
            ResponseDataItem item = posts.get(i);
            boolean isEven = (i % 2 == 0);

            if (item.getIs_hafal() == 100) {
                countHafal++;
            }

            itemList.add(new ItemModel(
                    item.getIdSurat(),
                    item.getNamaSurat(),
                    item.getArtiSurat(),
                    item.getJumlahAyat(),
                    item.getIdJuz(),
                    item.getNama_juz(),
                    item.getHalaman(),
                    item.getPosisi(),
                    item.getGambar(),
                    item.getId_detail_surat(),
                    item.getJumlah_ayat_potong(),
                    item.getUrl_audio(),
                    item.getUrl_video(),
                    item.getTipe(),
                    item.getIs_hafal(),
                    isEven
            ));
        }

        updateProgress(); // Perbarui tampilan progress

        ArrayList<String> arrayListNamaSurat = new ArrayList<>();
        ArrayList<String> arrayListUrl = new ArrayList<>();
        String namaJuz = intent.getStringExtra("nama_juz");

        adapter = new AdapterChallenge(context, itemList, arrayListNamaSurat, arrayListUrl, idUser, namaJuz); // Kirim nama juz
        adapter.setOnHafalStatusChangedListener(AudioChallenge.this); // Set listener untuk perubahan status hafal
        recyclerView.post(() -> recyclerView.scrollBy(0, scrollY)); // Scroll ke posisi sebelumnya
        recyclerView.setAdapter(adapter); // Set adapter ke RecyclerView
        recyclerView.postDelayed(this::animateLayoutTransition, 200);
    }

    /**
     * Menyimpan data surat ke database lokal untuk penggunaan offline.
     * Menghapus data lama sebelum menyimpan data baru.
     */
    private void saveToDatabase(List<ResponseDataItem> posts) {
        if (isActivityDestroyed || executorService.isShutdown()) return;

        executorService.execute(() -> {
            try {
                if (!isActivityDestroyed) {
                    List<HafalanEntity> entities = convertToEntities(posts);
                    // Hapus entri yang ada sebelum memasukkan yang baru
                    database.hafalanDao().deleteByJuzId(idJuz, idUser);
                    database.hafalanDao().insertAll(entities);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    /**
     * Memuat data surat dari database lokal.
     * Menampilkan data di RecyclerView jika tersedia, jika tidak menampilkan pesan toast.
     */
    private void loadFromDatabase() {
        executorService.execute(() -> {
            List<HafalanEntity> entities = database.hafalanDao().getAllByJuzIdAndUserId(idJuz, idUser);
            runOnUiThread(() -> {
                if (entities != null && !entities.isEmpty()) {
                    List<ResponseDataItem> items = convertToItems(entities);
                    processData(items); // Proses data dan tampilkan di RecyclerView
                } else {
                    Toast.makeText(AudioChallenge.this, "Data offline tidak tersedia", Toast.LENGTH_SHORT).show();
                }
            });
        });
        refreshSurat.setRefreshing(false);
    }

    /**
     * Mengonversi list ResponseDataItem menjadi list HafalanEntity.
     */
    private List<HafalanEntity> convertToEntities(List<ResponseDataItem> items) {
        List<HafalanEntity> entities = new ArrayList<>();
        for (ResponseDataItem item : items) {
            HafalanEntity entity = new HafalanEntity(
                    item.getIdSurat(),
                    item.getNamaSurat(),
                    item.getArtiSurat(),
                    item.getJumlahAyat(),
                    item.getIdJuz(),
                    item.getNama_juz(),
                    item.getHalaman(),
                    item.getPosisi(),
                    item.getGambar(),
                    item.getId_detail_surat(),
                    item.getJumlah_ayat_potong(),
                    item.getUrl_audio(),
                    item.getUrl_video(),
                    item.getTipe(),
                    item.getIs_hafal(),
                    idUser
            );
            entities.add(entity);
        }
        return entities;
    }

    /**
     * Mengonversi list HafalanEntity menjadi list ResponseDataItem.
     */
    private List<ResponseDataItem> convertToItems(List<HafalanEntity> entities) {
        List<ResponseDataItem> items = new ArrayList<>();
        for (HafalanEntity entity : entities) {
            ResponseDataItem item = new ResponseDataItem();
            item.setIdSurat(entity.getIdSurat());
            item.setNamaSurat(entity.getNamaSurat());
            item.setArtiSurat(entity.getArtiSurat());
            item.setJumlahAyat(entity.getJumlahAyat());
            item.setIdJuz(entity.getIdJuz());
            item.setNama_juz(entity.getNama_juz());
            item.setHalaman(entity.getHalaman());
            item.setPosisi(entity.getPosisi());
            item.setGambar(entity.getGambar());
            item.setId_detail_surat(entity.getId_detail_surat());
            item.setJumlah_ayat_potong(entity.getJumlah_ayat_potong());
            item.setUrl_audio(entity.getUrl_audio());
            item.setUrl_video(entity.getUrl_video());
            item.setTipe(entity.getTipe());
            item.setIs_hafal(entity.getIs_hafal());
            items.add(item);
        }
        return items;
    }

    /**
     * Memeriksa apakah perangkat terhubung ke jaringan.
     */
    private boolean isNetworkAvailable() {
        ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
        if (connectivityManager != null) {
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
        return false;
    }

    /**
     * Memperbarui tampilan progress hafalan.
     */
    private void updateProgress() {
        perjalanan.setText(countHafal + "/" + totalSurat + " Perjalanan");

        if (totalSurat > 0) {
            int percent = (int)((countHafal * 100.0f) / totalSurat);
            progressHafalan.setMax(100);
            progressHafalan.setProgress(percent);
            tvProgressPercent.setText(percent + "%");
        }
    }

    /**
     * Callback untuk perubahan status hafalan di adapter.
     */
    @Override
    public void onHafalStatusChanged(boolean isHafal) {
        if (isHafal) {
            countHafal++;
        } else {
            countHafal--;
        }

        updateProgress(); // Perbarui tampilan progress
    }

    /**
     * Refresh halaman dengan memuat ulang data surat.
     */
    public void refreshPage() {
        scrollY = recyclerView.computeVerticalScrollOffset();
        getDataSurat();
    }


    @Override
    protected void onResume() {
        super.onResume();
        refreshPage();
    }


    @Override
    protected void onDestroy() {
        isActivityDestroyed = true;
        if (executorService != null) {
            try {
                executorService.shutdown();
                if (!executorService.awaitTermination(500, TimeUnit.MILLISECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            } finally {
                super.onDestroy();
            }
        } else {
            super.onDestroy();
        }
    }
}