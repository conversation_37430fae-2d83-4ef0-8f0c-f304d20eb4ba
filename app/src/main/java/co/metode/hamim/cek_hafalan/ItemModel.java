package co.metode.hamim.cek_hafalan;

import co.metode.hamim.challenge.ChallengeViewHolder;

public class ItemModel {
    private String nama_juz;
    private String idSurat;
    private String namaSurat;
    private String artiSurat;
    private String jumlahAyat;
    private String idJuz;
    private String halaman;
    private String posisi;
    private String gambar;
    private String id_detail_surat;
    private String jumlah_ayat_potong;
    private String url_audio;
    private String url_video;
    private String tipe;
    private int is_hafal;
    private boolean isEven;
    private ChallengeViewHolder holder;

    public ItemModel(String idSurat, String namaSurat, String artiSurat, String jumlahAyat,
                     String idJuz, String nama_juz, String halaman, String posisi, String gambar,
                     String id_detail_surat, String jumlah_ayat_potong, String url_audio, String url_video,
                     String tipe, int is_hafal,boolean isEven) {
        this.idSurat = idSurat;
        this.namaSurat = namaSurat;
        this.artiSurat = artiSurat;
        this.jumlahAyat = jumlahAyat;
        this.idJuz = idJuz;
        this.nama_juz = nama_juz;
        this.halaman = halaman;
        this.posisi = posisi;
        this.gambar = gambar;
        this.id_detail_surat = id_detail_surat;
        this.jumlah_ayat_potong = jumlah_ayat_potong;
        this.url_audio = url_audio;
        this.url_video = url_video;
        this.tipe = tipe;
        this.is_hafal = is_hafal;
        this.isEven = isEven;
    }

    // Getter Methods
    public String getIdSurat() { return idSurat; }
    public String getNamaSurat() { return namaSurat; }
    public String getArtiSurat() { return artiSurat; }
    public String getJumlahAyat() { return jumlahAyat; }
    public String getIdJuz() { return idJuz; }
    public String getHalaman() { return halaman; }
    public String getPosisi() { return posisi; }
    public String getGambar() { return gambar; }
    public String getId_detail_surat() { return id_detail_surat; }
    public String getJumlah_ayat_potong() { return jumlah_ayat_potong; }
    public String getUrl_audio() { return url_audio; }
    public String getUrl_video() { return url_video; }
    public String getTipe() { return tipe; }
    public int getIs_hafal() { return is_hafal; }
    public void setIs_hafal(int is_hafal) {
        this.is_hafal = is_hafal;
    }
    public boolean isEven() {
        return isEven;
    }

    public String getNama_juz() {
        return nama_juz;
    }

    public void setNama_juz(String nama_juz) {
        this.nama_juz = nama_juz;
    }

    public void setHolder(ChallengeViewHolder holder) {
        this.holder = holder;
    }

    public ChallengeViewHolder getHolder() {
        return holder;
    }
}

