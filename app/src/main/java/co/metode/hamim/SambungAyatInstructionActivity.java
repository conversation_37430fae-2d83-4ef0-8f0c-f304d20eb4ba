package co.metode.hamim;

import android.content.Intent;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Random;

import co.metode.hamim.allMenuBeranda.murojaah.SambungAyat;
import co.metode.hamim.api.ApiClient;
import co.metode.hamim.api.ApiInterface;
import co.metode.hamim.mushaf.mushafnew.detailSurat.DataItemDetailSurat;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class SambungAyatInstructionActivity extends AppCompatActivity {

    private TextView titleText;
    private TextView instructionTitle;
    private TextView instructionDescription;
    private TextView noteTitle;
    private TextView noteDescription;
    private TextView actionText;
    private Button mulaiButton;
    private ConstraintLayout backButton;
    private ImageView hamimIcon;
    private ImageView micIcon;
    private TextView statusText;
    private android.widget.ProgressBar progressBar;

    // Ayat display views
    private androidx.cardview.widget.CardView ayatCard;
    private TextView ayatNumber;
    private TextView ayatText;
    private TextView ayatTranslation;

    private String idJuz;
    private String titleRange;
    private String suratNumber; // For API call to get ayat

    // State management
    private enum SambungAyatState {
        INITIAL,        // State 1: Ready to start
        HAMIM_SPEAKING, // State 2: Hamim is speaking
        USER_TURN       // State 3: User's turn to speak
    }

    private SambungAyatState currentState = SambungAyatState.INITIAL;

    // Assets bitmaps
    private Bitmap hamimBitmap;
    private Bitmap micBitmap;

    // Ayat data
    private List<DataItemDetailSurat> ayatList;
    private DataItemDetailSurat currentAyat;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_sambung_ayat_instruction);

        // Set status bar transparent
        setStatusBarTransparent();

        // Initialize views
        initViews();

        // Get intent data
        getIntentData();

        // Setup UI
        setupUI();

        // Setup click listeners
        setupClickListeners();
    }

    private void setStatusBarTransparent() {
        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
            View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        );
        getWindow().setStatusBarColor(android.graphics.Color.TRANSPARENT);
    }

    private void initViews() {
        titleText = findViewById(R.id.title_text);
        instructionTitle = findViewById(R.id.instruction_title);
        instructionDescription = findViewById(R.id.instruction_description);
        noteTitle = findViewById(R.id.note_title);
        noteDescription = findViewById(R.id.note_description);
        actionText = findViewById(R.id.action_text);
        mulaiButton = findViewById(R.id.mulai_button);
        backButton = findViewById(R.id.back_button);
        hamimIcon = findViewById(R.id.hamim_icon);
        micIcon = findViewById(R.id.mic_icon);
        statusText = findViewById(R.id.status_text);
        progressBar = findViewById(R.id.progress_bar);

        // Ayat display views
        ayatCard = findViewById(R.id.ayat_card);
        ayatNumber = findViewById(R.id.ayat_number);
        ayatText = findViewById(R.id.ayat_text);
        ayatTranslation = findViewById(R.id.ayat_translation);
    }

    private void getIntentData() {
        idJuz = getIntent().getStringExtra("id_juz");
        titleRange = getIntent().getStringExtra("title_range");
        suratNumber = getIntent().getStringExtra("surat_number");

        if (titleRange == null) {
            titleRange = "An-Naba 1-5"; // Default
        }

        // If surat_number is not provided, extract from title_range or use default
        if (suratNumber == null || suratNumber.isEmpty()) {
            // Extract surat number from title_range (e.g., "An-Naba 1-5" -> "78")
            // For now, use a default surat number (Al-Fatihah = 1)
            suratNumber = "1";
        }
    }

    private void setupUI() {
        // Set title
        titleText.setText("QS. " + titleRange);

        // Set instruction content
        instructionTitle.setText("Tarik napas dalam-dalam.");
        instructionDescription.setText("Di halaman ini, kamu akan sambung ayat bersama Hamim. Hamim akan membaca satu ayat, lalu giliran kamu melanjutkan ayat berikutnya.");

        // Set note content
        noteTitle.setText("Catatan:");
        noteDescription.setText("Pastikan suaramu jelas dan lingkungan sekitar tenang.");

        // Set action text
        actionText.setText("Tekan \"Mulai\" saat kamu siap.");

        // Load assets for bottom icons
        loadAssetImages();

        // Load ayat data
        loadAyatData();
    }

    private void loadAssetImages() {
        try {
            AssetManager assetManager = getAssets();

            // Load Hamim button image
            InputStream hamimInputStream = assetManager.open("button-player-hamim.png");
            hamimBitmap = BitmapFactory.decodeStream(hamimInputStream);
            hamimInputStream.close();

            // Load Mic button image
            InputStream micInputStream = assetManager.open("button-player-mic.png");
            micBitmap = BitmapFactory.decodeStream(micInputStream);
            micInputStream.close();

            // Set images to ImageViews
            hamimIcon.setImageBitmap(hamimBitmap);
            micIcon.setImageBitmap(micBitmap);

            // Set initial state
            updateUIForState(currentState);

        } catch (IOException e) {
            e.printStackTrace();
            // Fallback to drawable icons if assets fail to load
            hamimIcon.setImageResource(R.drawable.ic_person);
            micIcon.setImageResource(R.drawable.ic_mic);
        }
    }

    private void loadAyatData() {
        ApiInterface apiInterface = ApiClient.getOldClient().create(ApiInterface.class);
        Call<List<DataItemDetailSurat>> call = apiInterface.getAyat(suratNumber);

        call.enqueue(new Callback<List<DataItemDetailSurat>>() {
            @Override
            public void onResponse(Call<List<DataItemDetailSurat>> call, Response<List<DataItemDetailSurat>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ayatList = response.body();
                    Log.d("SambungAyat", "Loaded " + ayatList.size() + " ayat from surat " + suratNumber);
                } else {
                    Log.e("SambungAyat", "Failed to load ayat data");
                    Toast.makeText(SambungAyatInstructionActivity.this, "Gagal memuat data ayat", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onFailure(Call<List<DataItemDetailSurat>> call, Throwable t) {
                Log.e("SambungAyat", "API call failed: " + t.getMessage());
                Toast.makeText(SambungAyatInstructionActivity.this, "Gagal memuat data ayat", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupClickListeners() {
        // Back button
        backButton.setOnClickListener(v -> {
            onBackPressed();
        });

        // Mulai button
        mulaiButton.setOnClickListener(v -> {
            if (currentState == SambungAyatState.INITIAL) {
                if (ayatList == null || ayatList.isEmpty()) {
                    Toast.makeText(this, "Data ayat belum siap, silakan tunggu...", Toast.LENGTH_SHORT).show();
                    return;
                }

                // Start the sambung ayat process
                currentState = SambungAyatState.HAMIM_SPEAKING;

                // Get random ayat for Hamim to speak
                generateRandomAyat();

                updateUIForState(currentState);

                // Simulate Hamim speaking for 5 seconds, then switch to user turn
                new android.os.Handler().postDelayed(() -> {
                    currentState = SambungAyatState.USER_TURN;
                    updateUIForState(currentState);
                }, 5000);
            }
        });

        // Hamim icon (optional interaction)
        hamimIcon.setOnClickListener(v -> {
            // Could add some animation or interaction here
        });

        // Mic icon (optional interaction)
        micIcon.setOnClickListener(v -> {
            // Could add some animation or interaction here
        });
    }

    private void generateRandomAyat() {
        if (ayatList != null && !ayatList.isEmpty()) {
            Random random = new Random();
            int randomIndex = random.nextInt(ayatList.size());
            currentAyat = ayatList.get(randomIndex);
            Log.d("SambungAyat", "Generated ayat: " + currentAyat.getAyaNumber() + " - " + currentAyat.getAyaText());
        }
    }

    private void updateUIForState(SambungAyatState state) {
        switch (state) {
            case INITIAL:
                // State 1: Ready to start - both icons normal
                hamimIcon.setAlpha(1.0f);  // Normal opacity
                micIcon.setAlpha(1.0f);    // Normal opacity

                mulaiButton.setText("Mulai");
                mulaiButton.setVisibility(View.VISIBLE);
                if (statusText != null) statusText.setVisibility(View.GONE);
                actionText.setText("Tekan \"Mulai\" saat kamu siap.");
                progressBar.setProgress(0);

                // Show note sections, hide ayat card
                noteTitle.setVisibility(View.VISIBLE);
                noteDescription.setVisibility(View.VISIBLE);
                ayatCard.setVisibility(View.GONE);
                break;

            case HAMIM_SPEAKING:
                // State 2: Hamim is speaking (bright), Mic is faded
                hamimIcon.setAlpha(1.0f);    // Bright/Normal
                micIcon.setAlpha(0.4f);      // Faded

                mulaiButton.setVisibility(View.GONE);
                if (statusText != null) {
                    statusText.setText("DENGARKAN AYAT");
                    statusText.setVisibility(View.VISIBLE);
                }
                actionText.setText(""); // Remove text
                progressBar.setProgress(50);

                // Hide note sections, show ayat card with current ayat
                noteTitle.setVisibility(View.GONE);
                noteDescription.setVisibility(View.GONE);
                if (currentAyat != null) {
                    displayAyat(currentAyat);
                    ayatCard.setVisibility(View.VISIBLE);
                }
                break;

            case USER_TURN:
                // State 3: User's turn (Hamim faded, Mic bright)
                hamimIcon.setAlpha(0.4f);    // Faded
                micIcon.setAlpha(1.0f);      // Bright/Normal

                mulaiButton.setVisibility(View.GONE);
                if (statusText != null) {
                    statusText.setText("GILIRAN KAMU");
                    statusText.setVisibility(View.VISIBLE);
                }
                actionText.setText(""); // Remove text
                progressBar.setProgress(100);

                // Keep note sections hidden, keep ayat card visible
                noteTitle.setVisibility(View.GONE);
                noteDescription.setVisibility(View.GONE);
                // Ayat card stays visible to show what Hamim just read
                break;
        }
    }

    private void displayAyat(DataItemDetailSurat ayat) {
        if (ayat != null) {
            ayatNumber.setText(String.valueOf(ayat.getAyaNumber()));
            ayatText.setText(ayat.getAyaText());
            ayatTranslation.setText(ayat.getTranslationAyaText());
        }
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        finish();
    }
}
